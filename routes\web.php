<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Test route for project workspace
Route::get('/admin/project-workspace', function () {
    $project = request('project');
    if (!$project) {
        return redirect('/admin/projects');
    }

    // Redirect to the Filament page
    return redirect()->route('filament.admin.pages.project-workspace', ['project' => $project]);
});
