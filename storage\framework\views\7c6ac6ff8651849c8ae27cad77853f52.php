<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <!-- Project Header -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        <?php echo e($project->name); ?>

                    </h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">
                        <?php echo e($project->description); ?>

                    </p>
                    <div class="flex items-center gap-4 mt-3">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            <?php if($project->status === 'planning'): ?> bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                            <?php elseif($project->status === 'development'): ?> bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300
                            <?php elseif($project->status === 'review'): ?> bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                            <?php elseif($project->status === 'completed'): ?> bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                            <?php else: ?> bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                            <?php endif; ?>">
                            <?php echo e(ucfirst($project->status)); ?>

                        </span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">
                            Owner: <?php echo e($project->creator->name); ?>

                        </span>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500 dark:text-gray-400">Current Role</div>
                    <div class="text-lg font-semibold text-gray-900 dark:text-white">
                        <?php echo e($this->getRoleDisplayName($currentRole)); ?>

                    </div>
                </div>
            </div>
        </div>

        <!-- Role Switcher -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Choose Your Role
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->getAvailableRoles(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <button 
                        wire:click="switchRole('<?php echo e($role); ?>')"
                        class="p-4 rounded-lg border-2 transition-all duration-200 text-left
                            <?php if($currentRole === $role): ?> 
                                border-primary-500 bg-primary-50 dark:bg-primary-900/20 
                            <?php else: ?> 
                                border-gray-200 dark:border-gray-700 hover:border-primary-300 dark:hover:border-primary-600 
                            <?php endif; ?>">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="font-medium text-gray-900 dark:text-white"><?php echo e($label); ?></h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                    <?php echo e($this->getRoleDescription($role)); ?>

                                </p>
                            </div>
                            <!--[if BLOCK]><![endif]--><?php if(isset($roleProgress[$role]) && $roleProgress[$role]['completed']): ?>
                                <div class="ml-2">
                                    <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </button>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>

        <!-- Current Role Workspace -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                            <?php echo e($this->getRoleDisplayName($currentRole)); ?> Workspace
                        </h2>
                        <p class="text-gray-600 dark:text-gray-400 mt-1">
                            <?php echo e($this->getRoleDescription($currentRole)); ?>

                        </p>
                    </div>
                    <div class="flex gap-2">
                        <button class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Content
                        </button>
                    </div>
                </div>
            </div>

            <div class="p-6">
                <!-- AI Content Creator Component -->
                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('ai-content-creator', ['project' => $project, 'role' => $currentRole], key: $currentRole);

$__html = app('livewire')->mount($__name, $__params, 'lw-2734209463-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

                <!-- Existing Content List -->
                <div class="mt-8">
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('workspace-content-list', ['project' => $project, 'role' => $currentRole], key: "{$currentRole}-content");

$__html = app('livewire')->mount($__name, $__params, 'lw-2734209463-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH E:\laragon\www\filament\resources\views/filament/pages/project-workspace.blade.php ENDPATH**/ ?>